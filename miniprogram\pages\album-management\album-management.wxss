/**
 * 相册管理页面样式 - 三星相册风格
 *
 * 设计说明：
 * 1. 沉浸式设计，去除固定顶栏
 * 2. 现代化的卡片式布局
 * 3. 流畅的动画和交互效果
 * 4. 响应式设计，适配不同屏幕
 */

/**
 * 页面容器样式 - 沉浸式设计
 */
.album-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  display: flex;
  flex-direction: column;
}

/**
 * 顶部导航栏样式 - 仅返回按钮
 */
.top-nav {
  position: sticky;
  top: 0;
  z-index: 100;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  padding: 12rpx 24rpx;
  display: flex;
  align-items: center;
  min-height: 44rpx;
}

.nav-back {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.nav-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

/**
 * 主内容区域样式
 */
.main-content {
  flex: 1;
  padding: 0 24rpx 120rpx 24rpx; /* 底部留出标签栏空间 */
  overflow-y: auto;
}

.tab-content {
  min-height: 100%;
}

/**
 * 上传按钮区域样式
 */
.upload-section {
  margin: 24rpx 0;
  display: flex;
  align-items: center;
  gap: 24rpx;
}

.upload-btn {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 16rpx 24rpx;
  background: #fff;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 82, 217, 0.15);
  border: 2rpx solid #0052d9;
  transition: all 0.3s ease;
}

.upload-btn:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(0, 82, 217, 0.2);
}

.upload-btn-text {
  font-size: 28rpx;
  color: #0052d9;
  font-weight: 500;
}

.upload-status {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.upload-status-text {
  font-size: 26rpx;
  color: #666;
}

/**
 * 时间轴容器样式
 */
.timeline-container {
  margin-top: 24rpx;
}

.timeline-group {
  margin-bottom: 48rpx;
}

/**
 * 日期标签样式
 */
.date-label {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
  padding: 0 12rpx;
}

.date-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.date-count {
  font-size: 24rpx;
  color: #666;
  background: rgba(0, 0, 0, 0.05);
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
}

/**
 * 图片网格样式 - 现代化设计
 */
.images-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8rpx;
  margin-bottom: 24rpx;
}

.image-item {
  position: relative;
  aspect-ratio: 1;
  border-radius: 12rpx;
  overflow: hidden;
  background: #f5f5f5;
  transition: all 0.3s ease;
}

.image-item:active {
  transform: scale(0.98);
}

.image-item.selected {
  transform: scale(0.95);
  border: 4rpx solid #0052d9;
}

.grid-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.3s ease;
}

.trash-image {
  opacity: 0.7;
  filter: grayscale(0.3);
}

/**
 * 图片状态标识样式
 */
.selection-checkbox {
  position: absolute;
  top: 12rpx;
  left: 12rpx;
  z-index: 10;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 50%;
  padding: 4rpx;
}

.banner-icon {
  position: absolute;
  top: 12rpx;
  left: 12rpx;
  z-index: 10;
  background: rgba(0, 82, 217, 0.9);
  border-radius: 50%;
  width: 28rpx;
  height: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

/* 保留旧的banner-badge样式以防其他地方使用 */
.banner-badge {
  position: absolute;
  top: 12rpx;
  right: 12rpx;
  z-index: 10;
  background: #52c41a;
  color: white;
  border-radius: 50%;
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  font-weight: bold;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

/* 拖拽排序样式 */
.image-item.draggable {
  transition: transform 0.2s ease;
}

.image-item.draggable:active {
  transform: scale(1.05);
  z-index: 100;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.3);
}

/* 排序模式样式 */
.image-item.sortable {
  transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94),
              box-shadow 0.3s ease,
              border 0.3s ease,
              background 0.3s ease;
  border: 2rpx solid transparent;
  position: relative;
}

.image-item.sortable:not(.dragging) {
  transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* 拖拽状态样式 - 通过内联样式控制位置 */
.image-item.dragging {
  box-shadow: 0 20rpx 60rpx rgba(0, 82, 217, 0.5);
  border: 3rpx solid #0052d9;
  opacity: 0.95;
  transition: none; /* 拖拽时禁用过渡动画 */
}

/* 拖拽目标位置样式 */
.image-item.drag-over {
  transform: scale(0.9);
  border: 3rpx dashed #0052d9;
  background: rgba(0, 82, 217, 0.15);
  animation: dragOverPulse 0.6s ease-in-out infinite alternate;
}

/* 拖拽目标脉冲动画 */
@keyframes dragOverPulse {
  0% {
    background: rgba(0, 82, 217, 0.1);
    transform: scale(0.9);
  }
  100% {
    background: rgba(0, 82, 217, 0.2);
    transform: scale(0.95);
  }
}

/* 其他图片在拖拽时的动画 */
.image-item.sortable:not(.dragging):not(.drag-over) {
  transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.drag-handle {
  position: absolute;
  top: 8rpx;
  left: 8rpx;
  background: linear-gradient(135deg, rgba(0, 82, 217, 0.9), rgba(0, 82, 217, 1));
  border-radius: 50%;
  width: 36rpx;
  height: 36rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 11;
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 12rpx rgba(0, 82, 217, 0.4);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  animation: dragHandlePulse 2s ease-in-out infinite;
}

.drag-handle:active {
  background: linear-gradient(135deg, rgba(0, 82, 217, 1), rgba(0, 100, 255, 1));
  transform: scale(1.2);
  box-shadow: 0 6rpx 16rpx rgba(0, 82, 217, 0.6);
}

/* 拖拽手柄脉冲动画 */
@keyframes dragHandlePulse {
  0%, 100% {
    box-shadow: 0 4rpx 12rpx rgba(0, 82, 217, 0.4);
  }
  50% {
    box-shadow: 0 4rpx 12rpx rgba(0, 82, 217, 0.6), 0 0 0 4rpx rgba(0, 82, 217, 0.2);
  }
}

/* 排序序号显示 */
.sort-number {
  position: absolute;
  top: 12rpx;
  right: 12rpx;
  background: rgba(0, 82, 217, 0.9);
  color: white;
  border-radius: 50%;
  width: 28rpx;
  height: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  font-weight: bold;
  z-index: 10;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

/* 排序控制按钮 */
.sort-controls {
  position: absolute;
  top: 12rpx;
  right: 12rpx;
  z-index: 10;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.sort-btn {
  background: rgba(0, 82, 217, 0.8);
  border-radius: 50%;
  width: 28rpx;
  height: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease;
}

.sort-btn:active {
  background: rgba(0, 82, 217, 1);
  transform: scale(1.1);
}

.favorite-icon {
  position: absolute;
  bottom: 12rpx;
  right: 12rpx;
  z-index: 10;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  padding: 6rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.delete-time {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  font-size: 20rpx;
  padding: 8rpx;
  text-align: center;
}

.delete-time-text {
  font-size: 20rpx;
}

/**
 * 文件夹网格样式 - 相册APP风格
 */
.folders-grid {
  padding: 24rpx 16rpx;
}

.folder-section {
  margin-bottom: 48rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
  padding: 0 8rpx;
}

.folders-row {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12rpx;
}

/* 文件夹网格项 */
.folder-grid-item {
  display: flex;
  flex-direction: column;
  transition: transform 0.2s ease;
}

.folder-grid-item:active {
  transform: scale(0.95);
}

.folder-grid-item:active .cover-image {
  transform: scale(1.05);
}

/* 文件夹封面容器 */
.folder-cover {
  position: relative;
  width: 100%;
  aspect-ratio: 1;
  border-radius: 12rpx;
  overflow: hidden;
  background: #f8f9fa;
  margin-bottom: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

/* 封面图片容器 */
.cover-images {
  width: 100%;
  height: 100%;
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
  gap: 2rpx;
  background: #fff;
}

/* 封面图片 */
.cover-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.2s ease;
}

/* 回收站封面图片模糊效果 */
.folder-grid-item[data-system-type="trash"] .cover-image {
  filter: blur(3px);
  opacity: 0.7;
}

/* 回收站封面遮罩层 */
.folder-grid-item[data-system-type="trash"] .folder-cover::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  pointer-events: none;
}

/* 多图片布局 */
.cover-image-0 {
  grid-column: 1 / 3;
  grid-row: 1 / 2;
}

.cover-image-1 {
  grid-column: 1 / 2;
  grid-row: 2 / 3;
}

.cover-image-2 {
  grid-column: 2 / 3;
  grid-row: 2 / 3;
}

.cover-image-3 {
  grid-column: 1 / 3;
  grid-row: 2 / 3;
}

/* 单张图片时占满整个区域 */
.cover-images.single-image .cover-image {
  grid-column: 1 / 3;
  grid-row: 1 / 3;
}

/* 空文件夹状态 */
.empty-folder {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
}

.empty-folder .empty-text {
  font-size: 20rpx;
  color: #999;
}

/* 图片数量标识 */
.image-count-badge {
  position: absolute;
  bottom: 6rpx;
  right: 6rpx;
  background: rgba(0, 0, 0, 0.7);
  color: #fff;
  font-size: 20rpx;
  font-weight: 500;
  padding: 4rpx 8rpx;
  border-radius: 10rpx;
  min-width: 24rpx;
  text-align: center;
  backdrop-filter: blur(4rpx);
}

/* 文件夹信息 */
.folder-grid-info {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
  padding: 0 4rpx;
}

.folder-grid-name {
  font-size: 26rpx;
  font-weight: 500;
  color: #333;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.folder-grid-count {
  font-size: 22rpx;
  color: #666;
}

/* 响应式设计 */
/* 小屏幕设备（iPhone 5/SE等）显示2列 */
@media (max-width: 640rpx) {
  .folders-row {
    grid-template-columns: repeat(2, 1fr);
    gap: 16rpx;
  }
}

/* 大屏幕设备显示4列 */
@media (min-width: 1024rpx) {
  .folders-row {
    grid-template-columns: repeat(4, 1fr);
    gap: 10rpx;
  }
}

.folder-header {
  padding: 24rpx 0;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
  margin-bottom: 24rpx;
}

.folder-meta {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.folder-detail-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.folder-detail-count {
  font-size: 24rpx;
  color: #666;
}

.folder-tip {
  font-size: 22rpx;
  color: #0052d9;
  margin-top: 8rpx;
  padding: 8rpx 16rpx;
  background: rgba(0, 82, 217, 0.08);
  border-radius: 12rpx;
  display: inline-block;
}

/**
 * 回收站相关样式
 */
.trash-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  margin: 24rpx 0;
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.trash-stats {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.stats-text {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.expire-warning {
  font-size: 24rpx;
  color: #ff4757;
}

/**
 * 底部标签栏样式
 */
.bottom-tabs {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-top: 1rpx solid rgba(0, 0, 0, 0.05);
  display: flex;
  padding: 16rpx 0 calc(16rpx + env(safe-area-inset-bottom));
  z-index: 1000;
}

.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx;
  position: relative;
  transition: all 0.3s ease;
}

.tab-item:active {
  transform: scale(0.95);
}

.tab-text {
  font-size: 22rpx;
  color: #999;
  transition: color 0.3s ease;
}

.tab-item.active .tab-text {
  color: #0052d9;
  font-weight: 500;
}

/* 菜单标签特殊样式 */
.menu-tab {
  border-left: 1rpx solid rgba(0, 0, 0, 0.1);
}

.menu-tab:active {
  background: rgba(0, 0, 0, 0.05);
}

.tab-badge {
  position: absolute;
  top: 8rpx;
  right: 20rpx;
  background: #ff4757;
  color: white;
  font-size: 18rpx;
  padding: 4rpx 8rpx;
  border-radius: 12rpx;
  min-width: 24rpx;
  text-align: center;
}

/**
 * 批量操作工具栏样式
 */
.selection-toolbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1001;
  background: #fff;
  border-top: 1rpx solid rgba(0, 0, 0, 0.08);
  box-shadow: 0 -2rpx 20rpx rgba(0, 0, 0, 0.08);
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.selection-toolbar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 24rpx;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
}

.selection-info {
  display: flex;
  align-items: baseline;
  gap: 8rpx;
}

.count-text {
  font-size: 26rpx;
  color: #666;
}

.count-number {
  font-size: 30rpx;
  color: #0052d9;
  font-weight: 600;
}

.selection-actions {
  display: flex;
  gap: 16rpx;
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6rpx;
  padding: 12rpx 16rpx;
  border-radius: 12rpx;
  background: rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  min-width: 64rpx;
}

.action-btn:active {
  transform: scale(0.95);
}

.action-btn.favorite {
  background: rgba(255, 71, 87, 0.08);
}

.action-btn.favorite:active {
  background: rgba(255, 71, 87, 0.15);
}

.action-btn.classify {
  background: rgba(0, 82, 217, 0.08);
}

.action-btn.classify:active {
  background: rgba(0, 82, 217, 0.15);
}

.action-btn.danger {
  background: rgba(255, 71, 87, 0.08);
}

.action-btn.danger:active {
  background: rgba(255, 71, 87, 0.15);
}

.action-btn text {
  font-size: 20rpx;
  color: #333;
  font-weight: 500;
}

.action-btn.confirm {
  background: rgba(82, 196, 26, 0.08);
}

.action-btn.confirm:active {
  background: rgba(82, 196, 26, 0.15);
}

/* 排序模式工具栏 */
.sort-toolbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10rpx);
  border-top: 1rpx solid rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.sort-toolbar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 24rpx;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
}

.sort-info {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.sort-text {
  font-size: 26rpx;
  color: #0052d9;
  font-weight: 500;
}

.sort-actions {
  display: flex;
  gap: 16rpx;
}

/* 排序模式切换按钮 */
.sort-mode-toggle {
  margin-left: 16rpx;
  padding: 8rpx 16rpx;
  background: rgba(0, 82, 217, 0.1);
  border: 1rpx solid rgba(0, 82, 217, 0.3);
  border-radius: 16rpx;
  transition: all 0.3s ease;
}

.sort-mode-toggle:active {
  background: rgba(0, 82, 217, 0.2);
  transform: scale(0.95);
}

.toggle-text {
  font-size: 22rpx;
  color: #0052d9;
  font-weight: 500;
}

/* 兼容旧样式 */
.selection-info {
  flex: 1;
}

/**
 * 浮动卡片式菜单样式
 */
.more-menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  background: rgba(0, 0, 0, 0.2);
}

.more-menu {
  position: absolute;
  bottom: calc(100rpx + env(safe-area-inset-bottom));
  right: 16rpx;
  background: #fff;
  border-radius: 20rpx;
  box-shadow: 0 12rpx 48rpx rgba(0, 0, 0, 0.2), 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  overflow: hidden;
  min-width: 240rpx;
  max-width: 320rpx;
  animation: menuFloatIn 0.25s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  border: 1rpx solid rgba(0, 0, 0, 0.05);
}

/* 浮动卡片动画 */
@keyframes menuFloatIn {
  from {
    opacity: 0;
    transform: translateY(16rpx) scale(0.92);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 菜单项悬浮效果 */
.menu-item {
  transition: background-color 0.2s ease;
}

.menu-item:active {
  background-color: rgba(0, 82, 217, 0.08);
}

.menu-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 20rpx 24rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.06);
  transition: background-color 0.2s ease;
  position: relative;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item:active {
  background-color: rgba(0, 82, 217, 0.08);
}

.menu-item text {
  font-size: 30rpx;
  color: #333;
  font-weight: 400;
}

.menu-item.danger {
  color: #ff4757;
}

.menu-item.danger text {
  color: #ff4757;
}

.menu-divider {
  height: 1rpx;
  background: rgba(0, 0, 0, 0.08);
  margin: 12rpx 0;
}

.menu-section-title {
  font-size: 26rpx;
  color: #666;
  padding: 16rpx 24rpx 8rpx;
  font-weight: 500;
  font-weight: 500;
}

/**
 * 长按菜单样式
 */
.context-menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1001;
  background: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
}

.context-menu {
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.2);
  overflow: hidden;
  min-width: 280rpx;
  max-width: 400rpx;
  animation: contextMenuSlideIn 0.3s ease-out;
}

@keyframes contextMenuSlideIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.context-menu-title {
  padding: 24rpx;
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  text-align: center;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
}

.context-menu-actions {
  padding: 12rpx 0;
}

.context-action {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 20rpx 24rpx;
  transition: background 0.3s ease;
}

.context-action:active {
  background: rgba(0, 0, 0, 0.05);
}

.context-action.danger {
  color: #ff4757;
}

.context-action text {
  font-size: 28rpx;
  color: inherit;
}

/**
 * 文件夹选择对话框样式
 */
.folder-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1002;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
}

.folder-dialog-container {
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.2);
  overflow: hidden;
  width: 100%;
  max-width: 600rpx;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  animation: dialogSlideIn 0.3s ease-out;
}

@keyframes dialogSlideIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20rpx);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.folder-dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
}

.folder-dialog-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.folder-dialog-close {
  padding: 8rpx;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.05);
  transition: background 0.3s ease;
}

.folder-dialog-close:active {
  background: rgba(0, 0, 0, 0.1);
}

.folder-dialog-content {
  flex: 1;
  padding: 24rpx 32rpx;
  max-height: 400rpx;
  overflow-y: auto;
}

.folder-dialog-footer {
  display: flex;
  gap: 24rpx;
  padding: 24rpx 32rpx;
  border-top: 1rpx solid rgba(0, 0, 0, 0.05);
  background: #fafafa;
}

.folder-dialog-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 20rpx 0;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
  transition: background 0.3s ease;
}

.folder-dialog-item:last-child {
  border-bottom: none;
}

.folder-dialog-item:active {
  background: rgba(0, 0, 0, 0.05);
}

.folder-dialog-item.selected {
  background: rgba(0, 82, 217, 0.1);
}

.folder-dialog-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.folder-dialog-name {
  font-size: 28rpx;
  color: #333;
}

.folder-dialog-desc {
  font-size: 22rpx;
  color: #999;
}

.folder-dialog-item.trash-folder {
  border: 1rpx solid rgba(255, 71, 87, 0.2);
  background: rgba(255, 71, 87, 0.05);
}

.folder-dialog-item.trash-folder .folder-dialog-name {
  color: #ff4757;
}

/**
 * 空状态样式
 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
  text-align: center;
}

.empty-text {
  font-size: 32rpx;
  color: #666;
  margin: 24rpx 0 12rpx;
  font-weight: 500;
}

.empty-desc {
  font-size: 26rpx;
  color: #999;
  line-height: 1.5;
}

/**
 * 加载状态样式
 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx;
  gap: 16rpx;
}

.loading-text {
  font-size: 26rpx;
  color: #666;
}

/**
 * 分页加载状态样式
 */
.pagination-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx;
  gap: 12rpx;
}

.pagination-loading-text {
  font-size: 24rpx;
  color: #999;
}

/**
 * 响应式设计
 */
@media screen and (max-width: 750rpx) {
  .main-content {
    padding: 0 16rpx 120rpx 16rpx;
  }

  .images-grid {
    gap: 6rpx;
  }

  .upload-btn {
    padding: 12rpx 20rpx;
  }

  .upload-btn-text {
    font-size: 26rpx;
  }

  .date-text {
    font-size: 28rpx;
  }

  .date-count {
    font-size: 22rpx;
    padding: 6rpx 12rpx;
  }

  .folder-item {
    padding: 20rpx;
  }

  .folder-name {
    font-size: 28rpx;
  }

  .folder-count {
    font-size: 22rpx;
  }

  .tab-text {
    font-size: 20rpx;
  }

  .selection-count {
    font-size: 26rpx;
  }

  .menu-item text {
    font-size: 26rpx;
  }

  .context-action text {
    font-size: 26rpx;
  }

  .empty-text {
    font-size: 28rpx;
  }

  .empty-desc {
    font-size: 24rpx;
  }
}


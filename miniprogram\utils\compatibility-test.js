/**
 * 兼容性测试工具
 * 用于验证新系统与现有数据的兼容性
 */

const db = wx.cloud.database();

/**
 * 测试数据兼容性
 */
export async function testCompatibility() {
  console.log('开始兼容性测试...');
  
  const results = {
    dataStructure: false,
    bannerFunction: false,
    imageDisplay: false,
    folderSystem: false,
    trashSystem: false
  };
  
  try {
    // 1. 测试数据结构兼容性
    results.dataStructure = await testDataStructure();
    
    // 2. 测试首页图片功能
    results.bannerFunction = await testBannerFunction();
    
    // 3. 测试图片显示
    results.imageDisplay = await testImageDisplay();
    
    // 4. 测试文件夹系统
    results.folderSystem = await testFolderSystem();
    
    // 5. 测试回收站系统
    results.trashSystem = await testTrashSystem();
    
    console.log('兼容性测试完成:', results);
    return results;
  } catch (error) {
    console.error('兼容性测试失败:', error);
    return results;
  }
}

/**
 * 测试数据结构兼容性
 */
async function testDataStructure() {
  try {
    // 检查现有图片是否有新字段
    const result = await db.collection('album_images').limit(5).get();
    
    if (result.data.length === 0) {
      console.log('数据结构测试: 没有现有数据');
      return true;
    }
    
    const sample = result.data[0];
    const requiredFields = ['isFavorite', 'folderIds', 'isDeleted'];
    
    for (const field of requiredFields) {
      if (!sample.hasOwnProperty(field)) {
        console.error(`数据结构测试失败: 缺少字段 ${field}`);
        return false;
      }
    }
    
    console.log('数据结构测试: 通过');
    return true;
  } catch (error) {
    console.error('数据结构测试异常:', error);
    return false;
  }
}

/**
 * 测试首页图片功能
 */
async function testBannerFunction() {
  try {
    // 查询有bannerOrder的图片
    const result = await db.collection('album_images')
      .where({ 
        bannerOrder: db.command.neq(null),
        isDeleted: false
      })
      .orderBy('bannerOrder', 'asc')
      .get();
    
    console.log(`首页图片功能测试: 找到${result.data.length}张首页图片`);
    
    // 验证编号连续性
    const orders = result.data.map(img => img.bannerOrder).sort((a, b) => a - b);
    for (let i = 0; i < orders.length; i++) {
      if (orders[i] !== i + 1) {
        console.warn(`首页图片编号不连续: 期望${i + 1}, 实际${orders[i]}`);
      }
    }
    
    console.log('首页图片功能测试: 通过');
    return true;
  } catch (error) {
    console.error('首页图片功能测试异常:', error);
    return false;
  }
}

/**
 * 测试图片显示
 */
async function testImageDisplay() {
  try {
    // 测试获取临时链接
    const result = await db.collection('album_images')
      .where({ isDeleted: false })
      .limit(3)
      .get();
    
    if (result.data.length === 0) {
      console.log('图片显示测试: 没有图片数据');
      return true;
    }
    
    const fileList = result.data.map(item => item.fileID);
    const urlResult = await wx.cloud.getTempFileURL({ fileList });
    
    const successCount = urlResult.fileList.filter(file => file.tempFileURL).length;
    console.log(`图片显示测试: ${successCount}/${fileList.length} 张图片获取临时链接成功`);
    
    return successCount > 0;
  } catch (error) {
    console.error('图片显示测试异常:', error);
    return false;
  }
}

/**
 * 测试文件夹系统
 */
async function testFolderSystem() {
  try {
    // 检查系统文件夹是否存在
    const folderResult = await db.collection('album_folders')
      .where({ type: 'system' })
      .get();
    
    const systemFolders = folderResult.data;
    const requiredSystemFolders = ['favorite', 'banner'];
    
    for (const type of requiredSystemFolders) {
      const found = systemFolders.find(folder => folder.systemType === type);
      if (!found) {
        console.error(`文件夹系统测试失败: 缺少系统文件夹 ${type}`);
        return false;
      }
    }
    
    console.log(`文件夹系统测试: 找到${systemFolders.length}个系统文件夹`);
    return true;
  } catch (error) {
    console.error('文件夹系统测试异常:', error);
    return false;
  }
}

/**
 * 测试回收站系统
 */
async function testTrashSystem() {
  try {
    // 统计回收站图片
    const trashResult = await db.collection('album_images')
      .where({ isDeleted: true })
      .count();
    
    console.log(`回收站系统测试: 找到${trashResult.total}张已删除图片`);
    
    // 测试30天清理逻辑
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    
    const expiredResult = await db.collection('album_images')
      .where({
        isDeleted: true,
        deletedTime: db.command.lt(thirtyDaysAgo)
      })
      .count();
    
    console.log(`回收站系统测试: 找到${expiredResult.total}张过期图片`);
    return true;
  } catch (error) {
    console.error('回收站系统测试异常:', error);
    return false;
  }
}

/**
 * 性能测试
 */
export async function testPerformance() {
  console.log('开始性能测试...');
  
  const results = {
    imageLoadTime: 0,
    folderLoadTime: 0,
    searchTime: 0
  };
  
  try {
    // 测试图片加载性能
    const imageStart = Date.now();
    await db.collection('album_images')
      .where({ isDeleted: false })
      .orderBy('createTime', 'desc')
      .limit(20)
      .get();
    results.imageLoadTime = Date.now() - imageStart;
    
    // 测试文件夹加载性能
    const folderStart = Date.now();
    await db.collection('album_folders').get();
    results.folderLoadTime = Date.now() - folderStart;
    
    // 测试搜索性能
    const searchStart = Date.now();
    await db.collection('album_images')
      .where({
        isFavorite: true,
        isDeleted: false
      })
      .count();
    results.searchTime = Date.now() - searchStart;
    
    console.log('性能测试完成:', results);
    return results;
  } catch (error) {
    console.error('性能测试失败:', error);
    return results;
  }
}

/**
 * 数据完整性检查
 */
export async function checkDataIntegrity() {
  console.log('开始数据完整性检查...');
  
  const issues = [];
  
  try {
    // 检查孤立的文件夹关联
    const imagesResult = await db.collection('album_images')
      .where({ isDeleted: false })
      .get();
    
    const foldersResult = await db.collection('album_folders').get();
    const validFolderIds = foldersResult.data.map(f => f._id);
    
    for (const image of imagesResult.data) {
      if (image.folderIds && Array.isArray(image.folderIds)) {
        for (const folderId of image.folderIds) {
          if (!validFolderIds.includes(folderId)) {
            issues.push(`图片 ${image._id} 关联了不存在的文件夹 ${folderId}`);
          }
        }
      }
    }
    
    // 检查首页图片编号重复
    const bannerImages = imagesResult.data.filter(img => img.bannerOrder);
    const bannerOrders = bannerImages.map(img => img.bannerOrder);
    const uniqueOrders = [...new Set(bannerOrders)];
    
    if (bannerOrders.length !== uniqueOrders.length) {
      issues.push('存在重复的首页图片编号');
    }
    
    console.log(`数据完整性检查完成: 发现${issues.length}个问题`);
    if (issues.length > 0) {
      console.warn('数据完整性问题:', issues);
    }
    
    return { success: issues.length === 0, issues };
  } catch (error) {
    console.error('数据完整性检查失败:', error);
    return { success: false, issues: ['检查过程中发生错误'] };
  }
}
